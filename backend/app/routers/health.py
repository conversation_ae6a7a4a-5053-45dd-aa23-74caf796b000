"""
Health check router for CaseBuilder AI.
Simple endpoints for testing and monitoring.
"""

from fastapi import APIRouter
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/status")
async def get_status():
    """
    Simple status endpoint that always works.
    """
    return {
        "status": "healthy",
        "message": "CaseBuilder AI is running",
        "version": "2.0.0"
    }


@router.get("/tokens")
async def get_tokens_simple():
    """
    Simple tokens endpoint for testing.
    """
    return {
        "tokens_remaining": 2500,
        "analysis_cost": 1,
        "demand_generation_cost": 5,
        "message": "Token information retrieved successfully",
        "username": "demo_user"
    }


@router.post("/analysis")
async def simple_analysis(request_data: dict):
    """
    Simple analysis endpoint for testing.
    """
    try:
        logger.info(f"Received analysis request: {request_data.get('analysis_type', 'unknown')}")

        # Return a simple success response
        return {
            "status": "completed",
            "result": "**ANALYSIS COMPLETED**\n\nThis is a test analysis result. The system is working correctly.\n\n**SUMMARY**: Analysis completed successfully.",
            "analysis_type": request_data.get("analysis_type", "test"),
            "message": "Analysis completed successfully"
        }

    except Exception as e:
        logger.error(f"Error in simple analysis: {str(e)}")
        return {
            "status": "error",
            "result": f"Error: {str(e)}",
            "message": "Analysis failed"
        }
